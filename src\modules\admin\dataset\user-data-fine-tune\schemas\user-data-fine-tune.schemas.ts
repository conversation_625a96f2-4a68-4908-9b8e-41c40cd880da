import { z } from 'zod';
import {
  ProviderFineTuneEnum,
  DataFineTuneStatus,
  UserDataFineTuneSortBy,
  SortDirection,
} from '../types/user-data-fine-tune.types';

/**
 * Schema cho việc tạo mới user data fine tune
 */
export const CreateUserDataFineTuneSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên bộ dữ liệu không được để trống')
    .max(255, 'Tên bộ dữ liệu không được vượt quá 255 ký tự'),
  
  description: z
    .string()
    .optional(),
  
  provider: z
    .nativeEnum(ProviderFineTuneEnum, {
      errorMap: () => ({ message: 'Nhà cung cấp AI không hợp lệ' }),
    }),
  
  trainDataset: z
    .string()
    .min(1, 'Dữ liệu huấn luyện không được để trống'),
  
  validDataset: z
    .string()
    .optional(),
});

/**
 * Schema cho việc cập nhật user data fine tune
 */
export const UpdateUserDataFineTuneSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên bộ dữ liệu không được để trống')
    .max(255, 'Tên bộ dữ liệu không được vượt quá 255 ký tự')
    .optional(),
  
  description: z
    .string()
    .optional(),
  
  provider: z
    .nativeEnum(ProviderFineTuneEnum, {
      errorMap: () => ({ message: 'Nhà cung cấp AI không hợp lệ' }),
    })
    .optional(),
  
  trainDataset: z
    .string()
    .min(1, 'Dữ liệu huấn luyện không được để trống')
    .optional(),
  
  validDataset: z
    .string()
    .optional(),
});

/**
 * Schema cho việc truy vấn danh sách user data fine-tune
 */
export const UserDataFineTuneQuerySchema = z.object({
  page: z
    .number()
    .int()
    .min(1, 'Số trang phải lớn hơn 0')
    .optional()
    .default(1),
  
  limit: z
    .number()
    .int()
    .min(1, 'Số lượng item phải lớn hơn 0')
    .max(100, 'Số lượng item không được vượt quá 100')
    .optional()
    .default(10),
  
  search: z
    .string()
    .optional(),
  
  status: z
    .nativeEnum(DataFineTuneStatus, {
      errorMap: () => ({ message: 'Trạng thái không hợp lệ' }),
    })
    .optional(),
  
  sortBy: z
    .nativeEnum(UserDataFineTuneSortBy, {
      errorMap: () => ({ message: 'Trường sắp xếp không hợp lệ' }),
    })
    .optional()
    .default(UserDataFineTuneSortBy.CREATED_AT),
  
  sortDirection: z
    .nativeEnum(SortDirection, {
      errorMap: () => ({ message: 'Hướng sắp xếp không hợp lệ' }),
    })
    .optional()
    .default(SortDirection.DESC),
});

/**
 * Schema cho response của user data fine-tune
 */
export const UserDataFineTuneResponseSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
  name: z.string(),
  description: z.string().nullable(),
  createdAt: z.number(),
  status: z.nativeEnum(DataFineTuneStatus),
});

/**
 * Schema cho response chi tiết của user data fine-tune
 */
export const UserDataFineTuneDetailResponseSchema = UserDataFineTuneResponseSchema.extend({
  trainDatasetUrl: z.string().url('URL dữ liệu huấn luyện không hợp lệ'),
  validDatasetUrl: z.string().url('URL dữ liệu validation không hợp lệ').nullable(),
  provider: z.nativeEnum(ProviderFineTuneEnum),
});

/**
 * Schema cho response tạo dataset
 */
export const CreateDatasetResponseSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
  trainUploadUrl: z.string().url('URL upload không hợp lệ'),
  validUploadUrl: z.string().url('URL upload không hợp lệ').nullable(),
});

/**
 * Schema cho response upload URL
 */
export const UploadUrlResponseSchema = z.object({
  uploadUrl: z.string().url('URL upload không hợp lệ'),
});

/**
 * Schema cho response cập nhật trạng thái
 */
export const UpdateStatusResponseSchema = z.object({
  message: z.string(),
});

/**
 * Schema cho paginated result
 */
export const PaginatedResultSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    total: z.number().int().min(0),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    totalPages: z.number().int().min(0),
  });

/**
 * Schema cho query string parameters
 */
export const UploadUrlQuerySchema = z.object({
  mime: z
    .string()
    .min(1, 'MIME type không được để trống')
    .regex(/^application\/jsonl$/, 'MIME type phải là application/jsonl'),
});

/**
 * Schema cho update status parameters
 */
export const UpdateStatusParamsSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
});

export const UpdateStatusQuerySchema = z.object({
  status: z
    .boolean()
    .or(z.string().transform((val) => val === 'true')),
});

// Export types inferred from schemas
export type CreateUserDataFineTuneFormData = z.infer<typeof CreateUserDataFineTuneSchema>;
export type UpdateUserDataFineTuneFormData = z.infer<typeof UpdateUserDataFineTuneSchema>;
export type UserDataFineTuneQueryFormData = z.infer<typeof UserDataFineTuneQuerySchema>;
export type UploadUrlQueryFormData = z.infer<typeof UploadUrlQuerySchema>;
export type UpdateStatusParamsFormData = z.infer<typeof UpdateStatusParamsSchema>;
export type UpdateStatusQueryFormData = z.infer<typeof UpdateStatusQuerySchema>;
